#!/usr/bin/env python3
"""
1.5GHz微带线低通滤波器设计演示程序

演示项目的主要功能，包括理论分析、电路设计和基本仿真。
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from utils import ConfigManager, UnitConverter, logger
from microstrip import SubstrateProperties, MicrostripLine, MicrostripCalculator
from filter_design import ChebyshevFilter, FilterSpecifications
from topology import SteppedImpedanceResonator, FilterTopology, FilterLayout, ResonatorGeometry, CouplingStructure
from simulation import SParameterSimulator, SimulationParameters
from visualization import FilterVisualizer, ResultsPlotter


def demo_theoretical_analysis():
    """演示理论分析"""
    print("="*60)
    print("1. 理论分析演示")
    print("="*60)
    
    # 创建滤波器设计指标
    specs = FilterSpecifications(
        passband_frequency=1.5e9,
        stopband_frequency=2.0e9,
        passband_ripple=0.5,
        stopband_attenuation=20.0,
        source_impedance=50.0,
        load_impedance=50.0
    )
    
    # 创建切比雪夫滤波器设计
    filter_design = ChebyshevFilter(specs)
    
    print(f"滤波器阶数: {filter_design.order}")
    print(f"归一化元件值: {[f'{g:.4f}' for g in filter_design.element_values]}")
    
    # 分析频率响应
    frequencies = np.linspace(0.1e9, 3.0e9, 100)
    insertion_loss = []
    return_loss = []
    
    for freq in frequencies:
        il = filter_design.insertion_loss(freq)
        rl = filter_design.return_loss(freq)
        insertion_loss.append(il)
        return_loss.append(rl)
    
    # 绘制理论响应
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(frequencies/1e9, insertion_loss, 'r-', linewidth=2, label='插入损耗')
    plt.axhline(y=1.0, color='g', linestyle='--', alpha=0.7, label='通带要求 ≤ 1dB')
    plt.axhline(y=20.0, color='orange', linestyle='--', alpha=0.7, label='阻带要求 ≥ 20dB')
    plt.axvspan(0.1, 1.5, alpha=0.2, color='green', label='通带')
    plt.axvspan(2.0, 3.0, alpha=0.2, color='orange', label='阻带')
    plt.xlabel('频率 (GHz)')
    plt.ylabel('插入损耗 (dB)')
    plt.title('切比雪夫滤波器理论插入损耗')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 50)
    
    plt.subplot(2, 1, 2)
    plt.plot(frequencies/1e9, return_loss, 'b-', linewidth=2, label='回波损耗')
    plt.axhline(y=10.0, color='g', linestyle='--', alpha=0.7, label='通带要求 ≥ 10dB')
    plt.axvspan(0.1, 1.5, alpha=0.2, color='green', label='通带')
    plt.xlabel('频率 (GHz)')
    plt.ylabel('回波损耗 (dB)')
    plt.title('切比雪夫滤波器理论回波损耗')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.ylim(0, 40)
    
    plt.tight_layout()
    plt.savefig('results/theoretical_response.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return filter_design


def demo_microstrip_design():
    """演示微带线设计"""
    print("="*60)
    print("2. 微带线设计演示")
    print("="*60)
    
    # 创建基板属性
    substrate = SubstrateProperties(
        dielectric_constant=4.6,
        loss_tangent=0.02,
        thickness=UnitConverter.inch_to_meter(0.062),
        conductor_thickness=UnitConverter.mil_to_meter(1.4),
        conductor_conductivity=5.8e7
    )
    
    print(f"基板厚度: {UnitConverter.meter_to_mil(substrate.thickness):.1f} mil")
    print(f"介电常数: {substrate.dielectric_constant}")
    print(f"损耗角正切: {substrate.loss_tangent}")
    
    # 设计不同阻抗的微带线
    impedances = [20, 35, 50, 75, 100]
    widths = []
    
    for z0 in impedances:
        width = MicrostripCalculator.width_from_impedance(z0, substrate)
        widths.append(width)
        print(f"{z0}Ω微带线宽度: {UnitConverter.meter_to_mil(width):.1f} mil")
    
    # 分析频率特性
    line_50ohm = MicrostripLine(widths[2], substrate)  # 50欧姆线
    frequencies = np.linspace(0.1e9, 5.0e9, 100)
    
    er_eff = []
    z0_freq = []
    
    for freq in frequencies:
        er = line_50ohm.effective_dielectric_constant_frequency(freq)
        z0 = line_50ohm.characteristic_impedance_frequency(freq)
        er_eff.append(er)
        z0_freq.append(z0)
    
    # 绘制频率特性
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(frequencies/1e9, er_eff, 'b-', linewidth=2)
    plt.xlabel('频率 (GHz)')
    plt.ylabel('有效介电常数')
    plt.title('50Ω微带线有效介电常数')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.plot(frequencies/1e9, z0_freq, 'r-', linewidth=2)
    plt.xlabel('频率 (GHz)')
    plt.ylabel('特性阻抗 (Ω)')
    plt.title('50Ω微带线特性阻抗')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/microstrip_characteristics.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return substrate


def demo_sir_resonator(substrate):
    """演示阶梯阻抗谐振器设计"""
    print("="*60)
    print("3. 阶梯阻抗谐振器设计演示")
    print("="*60)
    
    # 创建SIR设计器
    sir_designer = SteppedImpedanceResonator(substrate, 1.5e9)
    
    print(f"1.5GHz时基板中波长: {sir_designer.wavelength*1e3:.2f} mm")
    
    # 设计不同参数的谐振器
    impedance_ratios = [2.0, 3.0, 4.0, 5.0]
    length_ratios = [0.2, 0.25, 0.3, 0.35]
    
    print("\n谐振器设计参数:")
    print("阻抗比\t长度比\t高阻抗宽度(mil)\t低阻抗宽度(mil)\t总长度(mm)")
    
    for i, (imp_ratio, len_ratio) in enumerate(zip(impedance_ratios, length_ratios)):
        resonator = sir_designer.design_sir_resonator(imp_ratio, len_ratio)
        
        high_width_mil = UnitConverter.meter_to_mil(resonator.high_impedance_width)
        low_width_mil = UnitConverter.meter_to_mil(resonator.low_impedance_width)
        total_length_mm = resonator.total_length * 1e3
        
        print(f"{imp_ratio:.1f}\t{len_ratio:.2f}\t{high_width_mil:.1f}\t\t{low_width_mil:.1f}\t\t{total_length_mm:.2f}")
    
    return sir_designer


def demo_simple_simulation(substrate):
    """演示简单仿真"""
    print("="*60)
    print("4. 简单仿真演示")
    print("="*60)
    
    # 创建简单的测试布局
    resonator = ResonatorGeometry(
        high_impedance_width=UnitConverter.mil_to_meter(50),
        low_impedance_width=UnitConverter.mil_to_meter(200),
        high_impedance_length=2e-3,
        low_impedance_length=8e-3,
        total_length=12e-3
    )
    
    layout = FilterLayout(
        resonators=[resonator],
        couplings=[],
        input_line=resonator,
        output_line=resonator,
        total_width=UnitConverter.mil_to_meter(200),
        total_length=24e-3,
        substrate=substrate
    )
    
    print(f"测试布局尺寸: {UnitConverter.meter_to_inch(layout.total_length):.3f} × "
          f"{UnitConverter.meter_to_inch(layout.total_width):.3f} 英寸")
    
    # 创建仿真参数
    sim_params = SimulationParameters(
        frequency_start=0.1e9,
        frequency_stop=3.0e9,
        frequency_points=201,
        port_impedance=50.0
    )
    
    # 运行仿真
    simulator = SParameterSimulator(layout, sim_params)
    network = simulator.simulate_filter()
    metrics = simulator.calculate_performance_metrics(network)
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(metrics['frequency']/1e9, metrics['s11_db'], 'b-', linewidth=2)
    plt.xlabel('频率 (GHz)')
    plt.ylabel('S11 (dB)')
    plt.title('S11 - 输入反射')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 2)
    plt.plot(metrics['frequency']/1e9, metrics['s21_db'], 'r-', linewidth=2)
    plt.xlabel('频率 (GHz)')
    plt.ylabel('S21 (dB)')
    plt.title('S21 - 传输')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 3)
    plt.plot(metrics['frequency']/1e9, metrics['insertion_loss'], 'g-', linewidth=2)
    plt.xlabel('频率 (GHz)')
    plt.ylabel('插入损耗 (dB)')
    plt.title('插入损耗')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(2, 2, 4)
    plt.plot(metrics['frequency']/1e9, metrics['vswr'], 'm-', linewidth=2)
    plt.xlabel('频率 (GHz)')
    plt.ylabel('VSWR')
    plt.title('电压驻波比')
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    
    plt.tight_layout()
    plt.savefig('results/simulation_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 输出关键性能指标
    freq_1_5ghz_idx = np.argmin(np.abs(metrics['frequency'] - 1.5e9))
    freq_2_5ghz_idx = np.argmin(np.abs(metrics['frequency'] - 2.5e9))
    
    il_1_5ghz = metrics['insertion_loss'][freq_1_5ghz_idx]
    rl_1_5ghz = metrics['return_loss'][freq_1_5ghz_idx]
    il_2_5ghz = metrics['insertion_loss'][freq_2_5ghz_idx]
    
    print(f"1.5GHz处性能: IL = {il_1_5ghz:.2f} dB, RL = {rl_1_5ghz:.2f} dB")
    print(f"2.5GHz处插入损耗: {il_2_5ghz:.2f} dB")
    
    return metrics


def main():
    """主演示函数"""
    print("1.5GHz微带线低通滤波器设计与仿真演示")
    print("="*60)
    
    # 创建结果目录
    results_dir = Path('results')
    results_dir.mkdir(exist_ok=True)
    
    try:
        # 1. 理论分析
        filter_design = demo_theoretical_analysis()
        
        # 2. 微带线设计
        substrate = demo_microstrip_design()
        
        # 3. SIR谐振器设计
        sir_designer = demo_sir_resonator(substrate)
        
        # 4. 简单仿真
        metrics = demo_simple_simulation(substrate)
        
        print("="*60)
        print("演示完成!")
        print("="*60)
        print("生成的文件:")
        print("  results/theoretical_response.png - 理论频率响应")
        print("  results/microstrip_characteristics.png - 微带线特性")
        print("  results/simulation_results.png - 仿真结果")
        
        return True
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
