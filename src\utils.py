"""
工具函数和配置管理模块

提供项目所需的工具函数和配置管理功能，包括：
- 配置文件加载和验证
- 单位转换工具
- 数学计算辅助函数
- 文件I/O操作
- 日志记录
"""

import yaml
import json
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Union, Optional, Tuple
from pathlib import Path
import logging
from dataclasses import dataclass, asdict
import warnings
from functools import lru_cache
import gc
import psutil
import os


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self._validate_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            if self.config_path.suffix.lower() == '.yaml' or self.config_path.suffix.lower() == '.yml':
                config = yaml.safe_load(f)
            elif self.config_path.suffix.lower() == '.json':
                config = json.load(f)
            else:
                raise ValueError(f"不支持的配置文件格式: {self.config_path.suffix}")
        
        return config
    
    def _validate_config(self):
        """验证配置文件"""
        required_sections = [
            'filter_specs',
            'substrate',
            'simulation',
            'optimization',
            'microstrip_filter'
        ]
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必需的节: {section}")
        
        # 验证滤波器设计指标
        filter_specs = self.config['filter_specs']
        required_filter_keys = ['cutoff_frequency', 'passband', 'stopband']
        for key in required_filter_keys:
            if key not in filter_specs:
                raise ValueError(f"滤波器设计指标缺少必需的参数: {key}")
        
        # 验证基板参数
        substrate = self.config['substrate']
        required_substrate_keys = ['dielectric_constant', 'loss_tangent', 'thickness']
        for key in required_substrate_keys:
            if key not in substrate:
                raise ValueError(f"基板参数缺少必需的参数: {key}")
        
        # 验证仿真参数
        simulation = self.config['simulation']
        required_sim_keys = ['frequency']
        for key in required_sim_keys:
            if key not in simulation:
                raise ValueError(f"仿真参数缺少必需的参数: {key}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self, output_path: Optional[str] = None):
        """
        保存配置文件
        
        Args:
            output_path: 输出路径，默认为原路径
        """
        if output_path is None:
            output_path = self.config_path
        else:
            output_path = Path(output_path)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            if output_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            elif output_path.suffix.lower() == '.json':
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的输出文件格式: {output_path.suffix}")


class UnitConverter:
    """单位转换工具"""
    
    # 长度转换 (转换为米)
    LENGTH_CONVERSIONS = {
        'm': 1.0,
        'mm': 1e-3,
        'cm': 1e-2,
        'inch': 0.0254,
        'mil': 25.4e-6,
        'um': 1e-6,
        'nm': 1e-9
    }
    
    # 频率转换 (转换为Hz)
    FREQUENCY_CONVERSIONS = {
        'Hz': 1.0,
        'kHz': 1e3,
        'MHz': 1e6,
        'GHz': 1e9,
        'THz': 1e12
    }
    
    # 阻抗转换 (转换为Ohm)
    IMPEDANCE_CONVERSIONS = {
        'ohm': 1.0,
        'Ω': 1.0,
        'kohm': 1e3,
        'mohm': 1e-3
    }
    
    @classmethod
    def convert_length(cls, value: float, from_unit: str, to_unit: str = 'm') -> float:
        """
        长度单位转换
        
        Args:
            value: 数值
            from_unit: 源单位
            to_unit: 目标单位
            
        Returns:
            转换后的数值
        """
        if from_unit not in cls.LENGTH_CONVERSIONS:
            raise ValueError(f"不支持的长度单位: {from_unit}")
        if to_unit not in cls.LENGTH_CONVERSIONS:
            raise ValueError(f"不支持的长度单位: {to_unit}")
        
        # 先转换为米，再转换为目标单位
        meters = value * cls.LENGTH_CONVERSIONS[from_unit]
        result = meters / cls.LENGTH_CONVERSIONS[to_unit]
        
        return result
    
    @classmethod
    def convert_frequency(cls, value: float, from_unit: str, to_unit: str = 'Hz') -> float:
        """
        频率单位转换
        
        Args:
            value: 数值
            from_unit: 源单位
            to_unit: 目标单位
            
        Returns:
            转换后的数值
        """
        if from_unit not in cls.FREQUENCY_CONVERSIONS:
            raise ValueError(f"不支持的频率单位: {from_unit}")
        if to_unit not in cls.FREQUENCY_CONVERSIONS:
            raise ValueError(f"不支持的频率单位: {to_unit}")
        
        # 先转换为Hz，再转换为目标单位
        hertz = value * cls.FREQUENCY_CONVERSIONS[from_unit]
        result = hertz / cls.FREQUENCY_CONVERSIONS[to_unit]
        
        return result
    
    @classmethod
    def mil_to_meter(cls, mil_value: float) -> float:
        """mil转米"""
        return mil_value * 25.4e-6
    
    @classmethod
    def meter_to_mil(cls, meter_value: float) -> float:
        """米转mil"""
        return meter_value / 25.4e-6
    
    @classmethod
    def inch_to_meter(cls, inch_value: float) -> float:
        """英寸转米"""
        return inch_value * 0.0254
    
    @classmethod
    def meter_to_inch(cls, meter_value: float) -> float:
        """米转英寸"""
        return meter_value / 0.0254
    
    @classmethod
    def ghz_to_hz(cls, ghz_value: float) -> float:
        """GHz转Hz"""
        return ghz_value * 1e9
    
    @classmethod
    def hz_to_ghz(cls, hz_value: float) -> float:
        """Hz转GHz"""
        return hz_value / 1e9


class MathUtils:
    """数学计算工具"""
    
    @staticmethod
    def db_to_linear(db_value: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """dB转线性值"""
        return 10**(db_value / 20)
    
    @staticmethod
    def linear_to_db(linear_value: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """线性值转dB"""
        return 20 * np.log10(np.abs(linear_value))
    
    @staticmethod
    def power_db_to_linear(db_value: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """功率dB转线性值"""
        return 10**(db_value / 10)
    
    @staticmethod
    def linear_to_power_db(linear_value: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """线性值转功率dB"""
        return 10 * np.log10(np.abs(linear_value))
    
    @staticmethod
    def vswr_to_reflection_coefficient(vswr: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """VSWR转反射系数"""
        return (vswr - 1) / (vswr + 1)
    
    @staticmethod
    def reflection_coefficient_to_vswr(gamma: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
        """反射系数转VSWR"""
        gamma_mag = np.abs(gamma)
        return (1 + gamma_mag) / (1 - gamma_mag)
    
    @staticmethod
    def parallel_impedance(z1: complex, z2: complex) -> complex:
        """并联阻抗计算"""
        return (z1 * z2) / (z1 + z2)
    
    @staticmethod
    def series_impedance(z1: complex, z2: complex) -> complex:
        """串联阻抗计算"""
        return z1 + z2
    
    @staticmethod
    def quality_factor(center_freq: float, bandwidth: float) -> float:
        """品质因子计算"""
        if bandwidth <= 0:
            return np.inf
        return center_freq / bandwidth
    
    @staticmethod
    def wavelength_in_medium(frequency: float, epsilon_r: float) -> float:
        """介质中的波长"""
        c = 3e8  # 光速
        return c / (frequency * np.sqrt(epsilon_r))
    
    @staticmethod
    def electrical_length(physical_length: float, wavelength: float) -> float:
        """电长度计算 (弧度)"""
        return 2 * np.pi * physical_length / wavelength
    
    @staticmethod
    def electrical_length_degrees(physical_length: float, wavelength: float) -> float:
        """电长度计算 (度)"""
        return 360 * physical_length / wavelength


class DataProcessor:
    """数据处理工具"""
    
    @staticmethod
    def smooth_data(data: np.ndarray, window_size: int = 5) -> np.ndarray:
        """
        数据平滑处理
        
        Args:
            data: 输入数据
            window_size: 窗口大小
            
        Returns:
            平滑后的数据
        """
        if window_size < 3:
            return data
        
        # 使用移动平均
        kernel = np.ones(window_size) / window_size
        smoothed = np.convolve(data, kernel, mode='same')
        
        return smoothed
    
    @staticmethod
    def find_peaks(data: np.ndarray, height: Optional[float] = None,
                  distance: Optional[int] = None) -> List[int]:
        """
        寻找峰值
        
        Args:
            data: 输入数据
            height: 最小峰值高度
            distance: 峰值间最小距离
            
        Returns:
            峰值索引列表
        """
        from scipy.signal import find_peaks as scipy_find_peaks
        
        peaks, _ = scipy_find_peaks(data, height=height, distance=distance)
        return peaks.tolist()
    
    @staticmethod
    def interpolate_data(x: np.ndarray, y: np.ndarray, 
                        x_new: np.ndarray, kind: str = 'linear') -> np.ndarray:
        """
        数据插值
        
        Args:
            x: 原始x坐标
            y: 原始y坐标
            x_new: 新的x坐标
            kind: 插值类型
            
        Returns:
            插值后的y坐标
        """
        from scipy.interpolate import interp1d
        
        f = interp1d(x, y, kind=kind, bounds_error=False, fill_value='extrapolate')
        return f(x_new)
    
    @staticmethod
    def calculate_statistics(data: np.ndarray) -> Dict[str, float]:
        """
        计算统计信息
        
        Args:
            data: 输入数据
            
        Returns:
            统计信息字典
        """
        return {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'median': np.median(data),
            'rms': np.sqrt(np.mean(data**2))
        }


class Logger:
    """日志记录器"""
    
    def __init__(self, name: str = 'FilterDesign', level: str = 'INFO'):
        """
        初始化日志记录器
        
        Args:
            name: 日志器名称
            level: 日志级别
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 创建控制台处理器
        if not self.logger.handlers:
            console_handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
    
    def info(self, message: str):
        """记录信息"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试信息"""
        self.logger.debug(message)


class PerformanceOptimizer:
    """性能优化工具类"""

    @staticmethod
    def get_optimal_thread_count() -> int:
        """获取最优线程数"""
        cpu_count = os.cpu_count()

        # 考虑内存限制
        memory_gb = psutil.virtual_memory().total / (1024**3)

        # 根据内存大小调整线程数
        if memory_gb < 4:
            max_threads = max(1, cpu_count // 2)
        elif memory_gb < 8:
            max_threads = cpu_count
        else:
            max_threads = min(cpu_count * 2, 16)  # 限制最大线程数

        return max_threads

    @staticmethod
    def optimize_numpy_settings():
        """优化NumPy设置"""
        # 设置NumPy线程数
        optimal_threads = PerformanceOptimizer.get_optimal_thread_count()
        os.environ['OMP_NUM_THREADS'] = str(optimal_threads)
        os.environ['MKL_NUM_THREADS'] = str(optimal_threads)
        os.environ['NUMEXPR_NUM_THREADS'] = str(optimal_threads)

        # 启用NumPy错误处理优化
        np.seterr(all='ignore')  # 忽略数值警告以提高性能

    @staticmethod
    def memory_cleanup():
        """内存清理"""
        gc.collect()

    @staticmethod
    def get_memory_usage() -> float:
        """获取当前内存使用率"""
        return psutil.virtual_memory().percent

    @staticmethod
    @lru_cache(maxsize=1000)
    def cached_calculation(func_name: str, *args) -> Any:
        """通用缓存计算装饰器"""
        # 这是一个示例，实际使用时需要根据具体函数实现
        pass


class ArrayOptimizer:
    """数组优化工具"""

    @staticmethod
    def optimize_array_dtype(arr: np.ndarray, target_precision: str = 'float32') -> np.ndarray:
        """优化数组数据类型以节省内存"""
        if arr.dtype == np.float64 and target_precision == 'float32':
            return arr.astype(np.float32)
        elif arr.dtype == np.complex128 and target_precision == 'complex64':
            return arr.astype(np.complex64)
        return arr

    @staticmethod
    def preallocate_arrays(shape: Tuple[int, ...], dtype: np.dtype, count: int) -> List[np.ndarray]:
        """预分配数组以减少内存分配开销"""
        return [np.empty(shape, dtype=dtype) for _ in range(count)]

    @staticmethod
    def use_memory_mapping(file_path: str, shape: Tuple[int, ...], dtype: np.dtype) -> np.ndarray:
        """使用内存映射处理大数组"""
        return np.memmap(file_path, dtype=dtype, mode='w+', shape=shape)


# 全局日志器实例
logger = Logger()

# 自动优化NumPy设置
PerformanceOptimizer.optimize_numpy_settings()
