"""
微带线传输线理论计算模块

基于严格的电磁场理论实现微带线特性参数计算，包括：
- 特性阻抗计算
- 有效介电常数计算  
- 传播常数计算
- 损耗计算（导体损耗和介质损耗）
- 频率色散效应

参考文献：
1. <PERSON>, E. O. "Equations for Microstrip Circuit Design"
2. <PERSON>, M. "Accurate Wide-Range Design Equations for the Frequency-Dependent Characteristic of Parallel Coupled Microstrip Lines"
3. <PERSON>, H. A. "Transmission-Line Properties of Parallel Strips Separated by a Dielectric Sheet"
"""

import numpy as np
import scipy.constants as const
from typing import Tuple, Union, Dict, Any
from dataclasses import dataclass
import warnings


@dataclass
class SubstrateProperties:
    """基板材料属性"""
    dielectric_constant: float      # 介电常数
    loss_tangent: float            # 损耗角正切
    thickness: float               # 厚度 (m)
    conductor_thickness: float     # 导体厚度 (m)
    conductor_conductivity: float  # 导体电导率 (S/m)
    

class MicrostripLine:
    """微带线传输线类"""
    
    def __init__(self, width: float, substrate: SubstrateProperties):
        """
        初始化微带线
        
        Args:
            width: 微带线宽度 (m)
            substrate: 基板属性
        """
        self.width = width
        self.substrate = substrate
        self._validate_parameters()
    
    def _validate_parameters(self):
        """验证参数有效性"""
        if self.width <= 0:
            raise ValueError("微带线宽度必须大于0")
        if self.substrate.thickness <= 0:
            raise ValueError("基板厚度必须大于0")
        if self.substrate.dielectric_constant < 1:
            raise ValueError("介电常数必须大于1")
        if self.substrate.loss_tangent < 0:
            raise ValueError("损耗角正切不能为负")
    
    def width_to_height_ratio(self) -> float:
        """计算宽高比 w/h"""
        return self.width / self.substrate.thickness
    
    def characteristic_impedance_static(self) -> float:
        """
        计算静态特性阻抗 (低频极限)
        使用Hammerstad-Jensen公式
        """
        w_h = self.width_to_height_ratio()
        er = self.substrate.dielectric_constant
        
        # Wheeler变换
        if w_h < 1:
            # 窄线情况
            z0 = (60 / np.sqrt(er)) * np.log(8 / w_h + w_h / 4)
        else:
            # 宽线情况  
            z0 = (120 * np.pi) / (np.sqrt(er) * (w_h + 1.393 + 0.667 * np.log(w_h + 1.444)))
        
        return z0
    
    def effective_dielectric_constant_static(self) -> float:
        """
        计算静态有效介电常数
        使用Hammerstad-Jensen公式
        """
        w_h = self.width_to_height_ratio()
        er = self.substrate.dielectric_constant
        
        # 计算填充因子
        if w_h < 1:
            # 窄线情况
            q = np.log(4 / w_h) / np.log(4)
        else:
            # 宽线情况
            q = 1 / (1 + 12 / w_h) + 0.04 * (1 - w_h)**2
        
        eff_er = (er + 1) / 2 + (er - 1) / 2 * q
        return eff_er
    
    def effective_dielectric_constant_frequency(self, frequency: float) -> float:
        """
        计算频率相关的有效介电常数
        考虑频率色散效应
        
        Args:
            frequency: 频率 (Hz)
        """
        er_static = self.effective_dielectric_constant_static()
        er = self.substrate.dielectric_constant
        w_h = self.width_to_height_ratio()
        h = self.substrate.thickness
        
        # 归一化频率
        c = 3e8  # 光速 m/s
        f_n = frequency * h * 1e-9 * np.sqrt(er - 1) / c
        
        # Kirschning-Jansen色散公式
        P1 = 0.27488 + (0.6315 + 0.525 / (1 + 0.0157 * f_n)**20) * w_h - 0.065683 * np.exp(-8.7513 * w_h)
        P2 = 0.33622 * (1 - np.exp(-0.03442 * er))
        P3 = 0.0363 * np.exp(-4.6 * w_h) * (1 - np.exp(-(f_n / 3.87)**4.97))
        P4 = 1 + 2.751 * (1 - np.exp(-(er / 15.916)**8))
        
        P = P1 * P2 * ((0.1844 + P3 * P4) * f_n)**1.5763
        
        er_eff_f = er - (er - er_static) / (1 + P)
        return er_eff_f
    
    def characteristic_impedance_frequency(self, frequency: float) -> float:
        """
        计算频率相关的特性阻抗
        
        Args:
            frequency: 频率 (Hz)
        """
        z0_static = self.characteristic_impedance_static()
        er_eff_static = self.effective_dielectric_constant_static()
        er_eff_f = self.effective_dielectric_constant_frequency(frequency)
        
        # 频率相关特性阻抗
        z0_f = z0_static * np.sqrt(er_eff_static / er_eff_f)
        return z0_f
    
    def propagation_constant(self, frequency: float) -> complex:
        """
        计算传播常数 γ = α + jβ
        
        Args:
            frequency: 频率 (Hz)
            
        Returns:
            复数传播常数
        """
        omega = 2 * np.pi * frequency
        er_eff = self.effective_dielectric_constant_frequency(frequency)
        
        # 相位常数
        c = 3e8  # 光速 m/s
        beta = omega * np.sqrt(er_eff) / c
        
        # 衰减常数
        alpha_c = self.conductor_attenuation(frequency)
        alpha_d = self.dielectric_attenuation(frequency)
        alpha = alpha_c + alpha_d
        
        return complex(alpha, beta)
    
    def conductor_attenuation(self, frequency: float) -> float:
        """
        计算导体损耗衰减常数
        
        Args:
            frequency: 频率 (Hz)
            
        Returns:
            导体衰减常数 (Np/m)
        """
        omega = 2 * np.pi * frequency
        mu0 = 4 * np.pi * 1e-7  # 真空磁导率 H/m
        sigma = float(self.substrate.conductor_conductivity)  # 确保是数值

        # 趋肤深度
        skin_depth = np.sqrt(2 / (omega * mu0 * sigma))
        
        # 表面电阻
        Rs = 1 / (sigma * skin_depth)
        
        # 微带线几何参数
        w = self.width
        h = self.substrate.thickness
        t = self.substrate.conductor_thickness
        z0 = self.characteristic_impedance_frequency(frequency)
        
        # Wheeler增量宽度修正
        if t > 0:
            delta_w = (t / np.pi) * np.log(1 + 4 * np.exp(1) / t * (np.tanh(np.sqrt(6.517 * w / t))**2))
            w_eff = w + delta_w
        else:
            w_eff = w
        
        # 导体损耗计算
        if w_eff / h < 1:
            # 窄线情况
            alpha_c = (Rs / (z0 * w_eff)) * (1 + w_eff / (3 * h))
        else:
            # 宽线情况
            alpha_c = Rs / (z0 * w_eff) * (1 + h / w_eff + 0.667 * h / w_eff * np.log(2 * w_eff / h))
        
        return alpha_c
    
    def dielectric_attenuation(self, frequency: float) -> float:
        """
        计算介质损耗衰减常数
        
        Args:
            frequency: 频率 (Hz)
            
        Returns:
            介质衰减常数 (Np/m)
        """
        omega = 2 * np.pi * frequency
        er = self.substrate.dielectric_constant
        er_eff = self.effective_dielectric_constant_frequency(frequency)
        tan_delta = self.substrate.loss_tangent
        
        # 介质损耗
        c = 3e8  # 光速 m/s
        alpha_d = (omega / (2 * c)) * np.sqrt(er_eff) * tan_delta * (er / er_eff) * ((er_eff - 1) / (er - 1))
        
        return alpha_d
    
    def phase_velocity(self, frequency: float) -> float:
        """
        计算相位速度
        
        Args:
            frequency: 频率 (Hz)
            
        Returns:
            相位速度 (m/s)
        """
        er_eff = self.effective_dielectric_constant_frequency(frequency)
        c = 3e8  # 光速 m/s
        vp = c / np.sqrt(er_eff)
        return vp
    
    def group_velocity(self, frequency: float, df: float = 1e6) -> float:
        """
        计算群速度
        
        Args:
            frequency: 频率 (Hz)
            df: 频率增量 (Hz)
            
        Returns:
            群速度 (m/s)
        """
        # 数值微分计算群速度
        gamma1 = self.propagation_constant(frequency - df/2)
        gamma2 = self.propagation_constant(frequency + df/2)
        
        dbeta_df = (gamma2.imag - gamma1.imag) / df
        vg = 2 * np.pi / dbeta_df
        
        return vg


class MicrostripCalculator:
    """微带线计算器工具类"""
    
    @staticmethod
    def width_from_impedance(z0_target: float, substrate: SubstrateProperties, 
                           tolerance: float = 1e-6, max_iterations: int = 100) -> float:
        """
        根据目标特性阻抗计算微带线宽度
        使用牛顿-拉夫逊迭代法
        
        Args:
            z0_target: 目标特性阻抗 (Ohm)
            substrate: 基板属性
            tolerance: 收敛容差
            max_iterations: 最大迭代次数
            
        Returns:
            微带线宽度 (m)
        """
        # 初始猜测值
        w = substrate.thickness  # 初始宽度设为基板厚度
        
        for i in range(max_iterations):
            line = MicrostripLine(w, substrate)
            z0_current = line.characteristic_impedance_static()
            
            # 检查收敛
            error = abs(z0_current - z0_target)
            if error < tolerance:
                return w
            
            # 计算导数 (数值微分)
            dw = w * 1e-6
            line_plus = MicrostripLine(w + dw, substrate)
            z0_plus = line_plus.characteristic_impedance_static()
            dz0_dw = (z0_plus - z0_current) / dw
            
            # 牛顿-拉夫逊更新
            if abs(dz0_dw) > 1e-12:
                w_new = w - (z0_current - z0_target) / dz0_dw
                
                # 确保宽度为正
                if w_new > 0:
                    w = w_new
                else:
                    w = w / 2
            else:
                break
        
        warnings.warn(f"宽度计算未收敛，当前误差: {error:.2e} Ohm")
        return w
    
    @staticmethod
    def coupled_microstrip_parameters(w: float, s: float, substrate: SubstrateProperties) -> Dict[str, float]:
        """
        计算耦合微带线参数
        
        Args:
            w: 微带线宽度 (m)
            s: 耦合间隙 (m)
            substrate: 基板属性
            
        Returns:
            耦合微带线参数字典
        """
        h = substrate.thickness
        er = substrate.dielectric_constant
        
        # 几何参数
        w_h = w / h
        s_h = s / h
        
        # 奇偶模有效介电常数
        if s_h <= 0.1:
            # 紧耦合情况
            er_eff_even = (er + 1) / 2 + (er - 1) / 2 * (1 + 12 / w_h)**(-0.5)
            er_eff_odd = er_eff_even
        else:
            # 松耦合情况
            er_eff_even = (er + 1) / 2 + (er - 1) / 2 * (1 + 12 / w_h)**(-0.5) + 0.04 * (1 - w_h)**2
            er_eff_odd = (er + 1) / 2 + (er - 1) / 2 * (1 + 12 / w_h)**(-0.5)
        
        # 奇偶模特性阻抗
        z0_even = 60 / np.sqrt(er_eff_even) * np.log(8 / w_h + w_h / 4)
        z0_odd = 60 / np.sqrt(er_eff_odd) * np.log(8 / w_h + w_h / 4)
        
        # 耦合系数
        k = (z0_even - z0_odd) / (z0_even + z0_odd)
        
        return {
            'z0_even': z0_even,
            'z0_odd': z0_odd,
            'er_eff_even': er_eff_even,
            'er_eff_odd': er_eff_odd,
            'coupling_coefficient': k
        }
