"""
多线程优化算法模块

实现基于遗传算法和粒子群算法的多线程参数优化，包括：
- 遗传算法优化器
- 粒子群优化器
- 多线程并行计算
- 目标函数定义
- 约束处理

参考文献：
1. <PERSON>, <PERSON><PERSON> <PERSON>. "Genetic Algorithms in Search, Optimization, and Machine Learning"
2. <PERSON>, <PERSON>. "Particle Swarm Optimization"
3. DEAP Documentation
"""

import numpy as np
from typing import List, Tuple, Dict, Callable, Optional, Any
from dataclasses import dataclass
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import time
import warnings
from abc import ABC, abstractmethod

# 优化算法库
from deap import base, creator, tools, algorithms
import random

try:
    from .microstrip import SubstrateProperties
    from .filter_design import ChebyshevFilter, FilterSpecifications
    from .topology import FilterTopology, FilterLayout
    from .simulation import SParameterSimulator, SimulationParameters, NetworkAnalyzer
except ImportError:
    from microstrip import SubstrateProperties
    from filter_design import ChebyshevFilter, FilterSpecifications
    from topology import FilterTopology, FilterLayout
    from simulation import SParameterSimulator, SimulationParameters, NetworkAnalyzer


@dataclass
class OptimizationParameters:
    """优化参数"""
    algorithm: str                    # 算法类型: 'genetic', 'particle_swarm'
    population_size: int              # 种群大小
    max_generations: int              # 最大代数
    crossover_rate: float             # 交叉概率
    mutation_rate: float              # 变异概率
    elite_ratio: float                # 精英比例
    num_threads: int                  # 线程数
    convergence_tolerance: float      # 收敛容差
    max_stagnation: int = 20          # 最大停滞代数


@dataclass
class DesignVariable:
    """设计变量"""
    name: str                         # 变量名
    min_value: float                  # 最小值
    max_value: float                  # 最大值
    initial_value: Optional[float] = None  # 初始值


@dataclass
class OptimizationResult:
    """优化结果"""
    best_individual: List[float]      # 最优个体
    best_fitness: float               # 最优适应度
    convergence_history: List[float]  # 收敛历史
    generation_count: int             # 迭代代数
    computation_time: float           # 计算时间
    success: bool                     # 是否成功


class ObjectiveFunction:
    """目标函数类"""
    
    def __init__(self, filter_specs: FilterSpecifications,
                 substrate: SubstrateProperties,
                 sim_params: SimulationParameters,
                 design_variables: List[DesignVariable],
                 weights: Dict[str, float]):
        """
        初始化目标函数
        
        Args:
            filter_specs: 滤波器设计指标
            substrate: 基板属性
            sim_params: 仿真参数
            design_variables: 设计变量
            weights: 目标函数权重
        """
        self.filter_specs = filter_specs
        self.substrate = substrate
        self.sim_params = sim_params
        self.design_variables = design_variables
        self.weights = weights
        
        # 创建切比雪夫滤波器设计
        self.chebyshev_filter = ChebyshevFilter(filter_specs)
        
        # 创建拓扑设计器
        self.topology_designer = FilterTopology(
            self.chebyshev_filter, substrate, filter_specs.passband_frequency
        )
    
    def evaluate(self, individual: List[float]) -> Tuple[float]:
        """
        评估个体适应度
        
        Args:
            individual: 个体参数
            
        Returns:
            适应度值 (单目标优化返回元组)
        """
        try:
            # 解码设计变量
            design_params = self._decode_individual(individual)
            
            # 生成滤波器布局
            layout = self._generate_layout(design_params)
            
            # 仿真S参数
            simulator = SParameterSimulator(layout, self.sim_params)
            network = simulator.simulate_filter()
            metrics = simulator.calculate_performance_metrics(network)
            
            # 计算目标函数
            fitness = self._calculate_fitness(metrics, layout)
            
            return (fitness,)
            
        except Exception as e:
            # 返回惩罚值
            return (1e6,)
    
    def _decode_individual(self, individual: List[float]) -> Dict[str, float]:
        """解码个体参数"""
        params = {}
        for i, var in enumerate(self.design_variables):
            # 将[0,1]范围映射到实际范围
            value = var.min_value + individual[i] * (var.max_value - var.min_value)
            params[var.name] = value
        return params
    
    def _generate_layout(self, design_params: Dict[str, float]) -> FilterLayout:
        """根据设计参数生成布局"""
        # 提取参数
        order = self.chebyshev_filter.order
        
        # 阻抗比参数
        impedance_ratios = []
        for i in range(order):
            key = f'impedance_ratio_{i}'
            if key in design_params:
                impedance_ratios.append(design_params[key])
            else:
                impedance_ratios.append(3.0)  # 默认值
        
        # 长度比参数
        length_ratios = []
        for i in range(order):
            key = f'length_ratio_{i}'
            if key in design_params:
                length_ratios.append(design_params[key])
            else:
                length_ratios.append(0.25)  # 默认值
        
        # 耦合间隙参数
        coupling_gaps = []
        for i in range(order - 1):
            key = f'coupling_gap_{i}'
            if key in design_params:
                coupling_gaps.append(design_params[key])
            else:
                coupling_gaps.append(20e-6)  # 默认值
        
        # 生成优化参数字典
        optimized_params = {
            'impedance_ratios': np.array(impedance_ratios),
            'length_ratios': np.array(length_ratios),
            'coupling_gaps': np.array(coupling_gaps)
        }
        
        # 生成布局
        layout = self.topology_designer._generate_layout(
            optimized_params, self.chebyshev_filter.element_values
        )
        
        return layout
    
    def _calculate_fitness(self, metrics: Dict[str, np.ndarray], 
                          layout: FilterLayout) -> float:
        """计算适应度函数"""
        fitness = 0.0
        frequency = metrics['frequency']
        
        # 通带性能
        passband_mask = (frequency >= 0.1e9) & (frequency <= 1.5e9)
        passband_il = metrics['insertion_loss'][passband_mask]
        passband_rl = metrics['return_loss'][passband_mask]
        
        # 通带插入损耗惩罚
        il_violation = np.maximum(0, passband_il - 1.0)  # 超过1dB的部分
        fitness += self.weights.get('passband_insertion_loss', 1.0) * np.sum(il_violation**2)
        
        # 通带回波损耗惩罚
        rl_violation = np.maximum(0, 10.0 - passband_rl)  # 小于10dB的部分
        fitness += self.weights.get('passband_return_loss', 1.0) * np.sum(rl_violation**2)
        
        # 阻带性能
        stopband_mask = (frequency >= 2.0e9) & (frequency <= 3.0e9)
        stopband_il = metrics['insertion_loss'][stopband_mask]
        
        # 阻带插入损耗惩罚
        stopband_violation = np.maximum(0, 20.0 - stopband_il)  # 小于20dB的部分
        fitness += self.weights.get('stopband_insertion_loss', 2.0) * np.sum(stopband_violation**2)
        
        # 尺寸约束
        max_length = 4.05 * 0.0254  # 4.05英寸转米
        max_width = 1.1 * 0.0254    # 1.1英寸转米
        
        length_violation = max(0, layout.total_length - max_length)
        width_violation = max(0, layout.total_width - max_width)
        
        fitness += self.weights.get('size_constraint', 0.5) * (length_violation**2 + width_violation**2) * 1e6
        
        return fitness


class BaseOptimizer(ABC):
    """优化器基类"""
    
    def __init__(self, objective_function: ObjectiveFunction,
                 design_variables: List[DesignVariable],
                 opt_params: OptimizationParameters):
        """
        初始化优化器
        
        Args:
            objective_function: 目标函数
            design_variables: 设计变量
            opt_params: 优化参数
        """
        self.objective_function = objective_function
        self.design_variables = design_variables
        self.opt_params = opt_params
        
        # 设置随机种子
        random.seed(42)
        np.random.seed(42)
    
    @abstractmethod
    def optimize(self) -> OptimizationResult:
        """执行优化"""
        pass
    
    def _parallel_evaluate(self, population: List[List[float]]) -> List[Tuple[float]]:
        """并行评估种群"""
        if self.opt_params.num_threads == 1:
            # 串行计算
            return [self.objective_function.evaluate(ind) for ind in population]
        else:
            # 并行计算
            with ThreadPoolExecutor(max_workers=self.opt_params.num_threads) as executor:
                futures = [executor.submit(self.objective_function.evaluate, ind) for ind in population]
                results = [future.result() for future in futures]
            return results


class GeneticOptimizer(BaseOptimizer):
    """遗传算法优化器"""
    
    def __init__(self, objective_function: ObjectiveFunction,
                 design_variables: List[DesignVariable],
                 opt_params: OptimizationParameters):
        super().__init__(objective_function, design_variables, opt_params)
        
        # 设置DEAP
        self._setup_deap()
    
    def _setup_deap(self):
        """设置DEAP遗传算法框架"""
        # 创建适应度和个体类
        if not hasattr(creator, "FitnessMin"):
            creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
        if not hasattr(creator, "Individual"):
            creator.create("Individual", list, fitness=creator.FitnessMin)
        
        # 工具箱
        self.toolbox = base.Toolbox()
        
        # 基因生成
        self.toolbox.register("attr_float", random.random)
        
        # 个体和种群生成
        self.toolbox.register("individual", tools.initRepeat, creator.Individual,
                             self.toolbox.attr_float, len(self.design_variables))
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # 遗传操作
        self.toolbox.register("evaluate", self.objective_function.evaluate)
        self.toolbox.register("mate", tools.cxTwoPoint)
        self.toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
        self.toolbox.register("select", tools.selTournament, tournsize=3)
    
    def optimize(self) -> OptimizationResult:
        """执行遗传算法优化"""
        start_time = time.time()
        
        # 初始化种群
        population = self.toolbox.population(n=self.opt_params.population_size)
        
        # 统计信息
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("min", np.min)
        stats.register("max", np.max)
        
        # 名人堂 (精英保留)
        hall_of_fame = tools.HallOfFame(int(self.opt_params.population_size * self.opt_params.elite_ratio))
        
        # 收敛历史
        convergence_history = []
        
        # 评估初始种群
        if self.opt_params.num_threads > 1:
            fitnesses = self._parallel_evaluate([ind[:] for ind in population])
            for ind, fit in zip(population, fitnesses):
                ind.fitness.values = fit
        else:
            fitnesses = list(map(self.toolbox.evaluate, population))
            for ind, fit in zip(population, fitnesses):
                ind.fitness.values = fit
        
        # 更新名人堂
        hall_of_fame.update(population)
        
        # 进化循环
        for generation in range(self.opt_params.max_generations):
            # 选择
            offspring = self.toolbox.select(population, len(population))
            offspring = list(map(self.toolbox.clone, offspring))
            
            # 交叉
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < self.opt_params.crossover_rate:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values
            
            # 变异
            for mutant in offspring:
                if random.random() < self.opt_params.mutation_rate:
                    self.toolbox.mutate(mutant)
                    # 确保变量在[0,1]范围内
                    for i in range(len(mutant)):
                        mutant[i] = max(0.0, min(1.0, mutant[i]))
                    del mutant.fitness.values
            
            # 评估需要评估的个体
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            if invalid_ind:
                if self.opt_params.num_threads > 1:
                    fitnesses = self._parallel_evaluate([ind[:] for ind in invalid_ind])
                    for ind, fit in zip(invalid_ind, fitnesses):
                        ind.fitness.values = fit
                else:
                    fitnesses = list(map(self.toolbox.evaluate, invalid_ind))
                    for ind, fit in zip(invalid_ind, fitnesses):
                        ind.fitness.values = fit
            
            # 替换种群
            population[:] = offspring
            
            # 更新名人堂
            hall_of_fame.update(population)
            
            # 记录统计信息
            record = stats.compile(population)
            best_fitness = hall_of_fame[0].fitness.values[0]
            convergence_history.append(best_fitness)
            
            # 检查收敛
            if len(convergence_history) > self.opt_params.max_stagnation:
                recent_history = convergence_history[-self.opt_params.max_stagnation:]
                if max(recent_history) - min(recent_history) < self.opt_params.convergence_tolerance:
                    print(f"收敛于第 {generation} 代")
                    break
            
            if generation % 10 == 0:
                print(f"第 {generation} 代: 最优适应度 = {best_fitness:.6f}")
        
        computation_time = time.time() - start_time
        
        # 返回结果
        best_individual = hall_of_fame[0][:]
        best_fitness = hall_of_fame[0].fitness.values[0]
        
        return OptimizationResult(
            best_individual=best_individual,
            best_fitness=best_fitness,
            convergence_history=convergence_history,
            generation_count=generation + 1,
            computation_time=computation_time,
            success=best_fitness < 1e5
        )


class ParticleSwarmOptimizer(BaseOptimizer):
    """粒子群优化器"""

    def __init__(self, objective_function: ObjectiveFunction,
                 design_variables: List[DesignVariable],
                 opt_params: OptimizationParameters):
        super().__init__(objective_function, design_variables, opt_params)

        # PSO参数
        self.inertia_weight = 0.9
        self.cognitive_coeff = 2.0
        self.social_coeff = 2.0
        self.velocity_clamp = 0.1

    def optimize(self) -> OptimizationResult:
        """执行粒子群优化"""
        start_time = time.time()

        # 初始化粒子群
        swarm_size = self.opt_params.population_size
        dimensions = len(self.design_variables)

        # 粒子位置和速度
        positions = np.random.rand(swarm_size, dimensions)
        velocities = np.random.rand(swarm_size, dimensions) * 0.1 - 0.05

        # 个体最优和全局最优
        personal_best_positions = positions.copy()
        personal_best_fitness = np.full(swarm_size, np.inf)
        global_best_position = None
        global_best_fitness = np.inf

        # 收敛历史
        convergence_history = []

        # 主循环
        for iteration in range(self.opt_params.max_generations):
            # 评估粒子适应度
            if self.opt_params.num_threads > 1:
                fitnesses = self._parallel_evaluate([pos.tolist() for pos in positions])
                fitness_values = [fit[0] for fit in fitnesses]
            else:
                fitness_values = []
                for pos in positions:
                    fitness = self.objective_function.evaluate(pos.tolist())[0]
                    fitness_values.append(fitness)

            # 更新个体最优
            for i, fitness in enumerate(fitness_values):
                if fitness < personal_best_fitness[i]:
                    personal_best_fitness[i] = fitness
                    personal_best_positions[i] = positions[i].copy()

                # 更新全局最优
                if fitness < global_best_fitness:
                    global_best_fitness = fitness
                    global_best_position = positions[i].copy()

            convergence_history.append(global_best_fitness)

            # 更新速度和位置
            for i in range(swarm_size):
                # 随机因子
                r1 = np.random.rand(dimensions)
                r2 = np.random.rand(dimensions)

                # 速度更新
                cognitive_component = self.cognitive_coeff * r1 * (personal_best_positions[i] - positions[i])
                social_component = self.social_coeff * r2 * (global_best_position - positions[i])

                velocities[i] = (self.inertia_weight * velocities[i] +
                               cognitive_component + social_component)

                # 速度限制
                velocities[i] = np.clip(velocities[i], -self.velocity_clamp, self.velocity_clamp)

                # 位置更新
                positions[i] += velocities[i]

                # 边界处理
                positions[i] = np.clip(positions[i], 0.0, 1.0)

            # 检查收敛
            if len(convergence_history) > self.opt_params.max_stagnation:
                recent_history = convergence_history[-self.opt_params.max_stagnation:]
                if max(recent_history) - min(recent_history) < self.opt_params.convergence_tolerance:
                    print(f"收敛于第 {iteration} 代")
                    break

            if iteration % 10 == 0:
                print(f"第 {iteration} 代: 最优适应度 = {global_best_fitness:.6f}")

        computation_time = time.time() - start_time

        return OptimizationResult(
            best_individual=global_best_position.tolist(),
            best_fitness=global_best_fitness,
            convergence_history=convergence_history,
            generation_count=iteration + 1,
            computation_time=computation_time,
            success=global_best_fitness < 1e5
        )


class OptimizationManager:
    """优化管理器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化优化管理器

        Args:
            config: 配置字典
        """
        self.config = config

        # 创建设计指标
        self.filter_specs = self._create_filter_specifications()

        # 创建基板属性
        self.substrate = self._create_substrate_properties()

        # 创建仿真参数
        self.sim_params = self._create_simulation_parameters()

        # 创建设计变量
        self.design_variables = self._create_design_variables()

        # 创建优化参数
        self.opt_params = self._create_optimization_parameters()

    def _create_filter_specifications(self) -> FilterSpecifications:
        """创建滤波器设计指标"""
        specs_config = self.config['filter_specs']

        return FilterSpecifications(
            passband_frequency=specs_config['cutoff_frequency'] * 1e9,
            stopband_frequency=specs_config['stopband']['frequency_range'][0] * 1e9,
            passband_ripple=specs_config['passband_ripple'],
            stopband_attenuation=specs_config['stopband']['min_insertion_loss'],
            source_impedance=50.0,
            load_impedance=50.0
        )

    def _create_substrate_properties(self) -> SubstrateProperties:
        """创建基板属性"""
        substrate_config = self.config['substrate']

        return SubstrateProperties(
            dielectric_constant=substrate_config['dielectric_constant'],
            loss_tangent=substrate_config['loss_tangent'],
            thickness=substrate_config['thickness'] * 0.0254,  # 英寸转米
            conductor_thickness=substrate_config.get('conductor_thickness', 1.4) * 25.4e-6,  # mil转米
            conductor_conductivity=substrate_config.get('conductor_conductivity', 5.8e7)
        )

    def _create_simulation_parameters(self) -> SimulationParameters:
        """创建仿真参数"""
        sim_config = self.config['simulation']

        return SimulationParameters(
            frequency_start=sim_config['frequency']['start'] * 1e9,
            frequency_stop=sim_config['frequency']['stop'] * 1e9,
            frequency_points=sim_config['frequency']['points'],
            port_impedance=sim_config['port_impedance']
        )

    def _create_design_variables(self) -> List[DesignVariable]:
        """创建设计变量"""
        # 创建切比雪夫滤波器以获取阶数
        chebyshev_filter = ChebyshevFilter(self.filter_specs)
        order = chebyshev_filter.order

        variables = []

        # 阻抗比变量
        imp_range = self.config['microstrip_filter']['sir_params']['impedance_ratio_range']
        for i in range(order):
            variables.append(DesignVariable(
                name=f'impedance_ratio_{i}',
                min_value=imp_range[0],
                max_value=imp_range[1]
            ))

        # 长度比变量
        len_range = self.config['microstrip_filter']['sir_params']['length_ratio_range']
        for i in range(order):
            variables.append(DesignVariable(
                name=f'length_ratio_{i}',
                min_value=len_range[0],
                max_value=len_range[1]
            ))

        # 耦合间隙变量
        gap_range = self.config['microstrip_filter']['sir_params']['coupling_gap_range']
        gap_range_m = [gap * 25.4e-6 for gap in gap_range]  # mil转米
        for i in range(order - 1):
            variables.append(DesignVariable(
                name=f'coupling_gap_{i}',
                min_value=gap_range_m[0],
                max_value=gap_range_m[1]
            ))

        return variables

    def _create_optimization_parameters(self) -> OptimizationParameters:
        """创建优化参数"""
        opt_config = self.config['optimization']

        if opt_config['algorithm'] == 'genetic':
            genetic_config = opt_config['genetic']
            return OptimizationParameters(
                algorithm='genetic',
                population_size=genetic_config['population_size'],
                max_generations=genetic_config['generations'],
                crossover_rate=genetic_config['crossover_rate'],
                mutation_rate=genetic_config['mutation_rate'],
                elite_ratio=genetic_config['elite_ratio'],
                num_threads=opt_config['num_threads'],
                convergence_tolerance=1e-6
            )
        elif opt_config['algorithm'] == 'particle_swarm':
            pso_config = opt_config['particle_swarm']
            return OptimizationParameters(
                algorithm='particle_swarm',
                population_size=pso_config['swarm_size'],
                max_generations=pso_config['max_iterations'],
                crossover_rate=0.0,  # PSO不使用
                mutation_rate=0.0,   # PSO不使用
                elite_ratio=0.0,     # PSO不使用
                num_threads=opt_config['num_threads'],
                convergence_tolerance=1e-6
            )
        else:
            raise ValueError(f"不支持的优化算法: {opt_config['algorithm']}")

    def run_optimization(self) -> OptimizationResult:
        """运行优化"""
        # 创建目标函数
        weights = self.config['optimization']['objective_weights']
        objective_function = ObjectiveFunction(
            self.filter_specs, self.substrate, self.sim_params,
            self.design_variables, weights
        )

        # 创建优化器
        if self.opt_params.algorithm == 'genetic':
            optimizer = GeneticOptimizer(objective_function, self.design_variables, self.opt_params)
        elif self.opt_params.algorithm == 'particle_swarm':
            optimizer = ParticleSwarmOptimizer(objective_function, self.design_variables, self.opt_params)
        else:
            raise ValueError(f"不支持的优化算法: {self.opt_params.algorithm}")

        # 执行优化
        print(f"开始{self.opt_params.algorithm}优化...")
        print(f"设计变量数量: {len(self.design_variables)}")
        print(f"种群大小: {self.opt_params.population_size}")
        print(f"最大代数: {self.opt_params.max_generations}")
        print(f"线程数: {self.opt_params.num_threads}")

        result = optimizer.optimize()

        print(f"优化完成!")
        print(f"最优适应度: {result.best_fitness:.6f}")
        print(f"计算时间: {result.computation_time:.2f} 秒")
        print(f"迭代代数: {result.generation_count}")

        return result
