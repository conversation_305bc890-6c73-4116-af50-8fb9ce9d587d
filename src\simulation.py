"""
S参数仿真引擎模块

基于传输矩阵方法和scikit-rf库实现微带线滤波器的S参数计算，包括：
- 传输矩阵级联计算
- S参数转换
- 插入损耗和回波损耗计算
- 频率响应分析
- 群延迟计算

参考文献：
1. <PERSON><PERSON>, <PERSON><PERSON> <PERSON>. "Microwave Engineering"
2. <PERSON>, <PERSON><PERSON> "Foundations for Microwave Engineering"
3. scikit-rf Documentation
"""

import numpy as np
import skrf as rf
from typing import List, Tuple, Dict, Optional, Union
from dataclasses import dataclass
import warnings

try:
    from .microstrip import MicrostripLine, SubstrateProperties
    from .topology import FilterLayout, ResonatorGeometry, CouplingStructure
except ImportError:
    from microstrip import MicrostripLine, SubstrateProperties
    from topology import FilterLayout, ResonatorGeometry, CouplingStructure


@dataclass
class SimulationParameters:
    """仿真参数"""
    frequency_start: float        # 起始频率 (Hz)
    frequency_stop: float         # 终止频率 (Hz)
    frequency_points: int         # 频率点数
    port_impedance: float         # 端口阻抗 (Ohm)
    temperature: float = 25.0     # 温度 (°C)


class TransmissionMatrix:
    """传输矩阵计算类"""
    
    @staticmethod
    def transmission_line_abcd(gamma: complex, z0: complex, length: float) -> np.ndarray:
        """
        计算传输线ABCD矩阵
        
        Args:
            gamma: 传播常数
            z0: 特性阻抗
            length: 长度
            
        Returns:
            2x2 ABCD矩阵
        """
        cosh_gl = np.cosh(gamma * length)
        sinh_gl = np.sinh(gamma * length)
        
        A = cosh_gl
        B = z0 * sinh_gl
        C = sinh_gl / z0
        D = cosh_gl
        
        return np.array([[A, B], [C, D]], dtype=complex)
    
    @staticmethod
    def series_impedance_abcd(Z: complex) -> np.ndarray:
        """
        计算串联阻抗ABCD矩阵
        
        Args:
            Z: 串联阻抗
            
        Returns:
            2x2 ABCD矩阵
        """
        return np.array([[1, Z], [0, 1]], dtype=complex)
    
    @staticmethod
    def shunt_admittance_abcd(Y: complex) -> np.ndarray:
        """
        计算并联导纳ABCD矩阵
        
        Args:
            Y: 并联导纳
            
        Returns:
            2x2 ABCD矩阵
        """
        return np.array([[1, 0], [Y, 1]], dtype=complex)
    
    @staticmethod
    def cascade_abcd(matrices: List[np.ndarray]) -> np.ndarray:
        """
        级联ABCD矩阵
        
        Args:
            matrices: ABCD矩阵列表
            
        Returns:
            级联后的ABCD矩阵
        """
        result = matrices[0]
        for matrix in matrices[1:]:
            result = np.dot(result, matrix)
        return result
    
    @staticmethod
    def abcd_to_s_parameters(abcd: np.ndarray, z0: float = 50.0) -> np.ndarray:
        """
        将ABCD矩阵转换为S参数
        
        Args:
            abcd: ABCD矩阵
            z0: 参考阻抗
            
        Returns:
            2x2 S参数矩阵
        """
        A, B, C, D = abcd[0, 0], abcd[0, 1], abcd[1, 0], abcd[1, 1]
        
        # 转换公式
        denominator = A + B/z0 + C*z0 + D
        
        S11 = (A + B/z0 - C*z0 - D) / denominator
        S12 = 2 * (A*D - B*C) / denominator
        S21 = 2 / denominator
        S22 = (-A + B/z0 - C*z0 + D) / denominator
        
        return np.array([[S11, S12], [S21, S22]], dtype=complex)


class SParameterSimulator:
    """S参数仿真器"""
    
    def __init__(self, layout: FilterLayout, sim_params: SimulationParameters):
        """
        初始化S参数仿真器
        
        Args:
            layout: 滤波器布局
            sim_params: 仿真参数
        """
        self.layout = layout
        self.sim_params = sim_params
        self.frequency = np.linspace(
            sim_params.frequency_start,
            sim_params.frequency_stop,
            sim_params.frequency_points
        )
        
        # 创建scikit-rf频率对象
        self.freq_obj = rf.Frequency.from_f(
            self.frequency / 1e9,  # 转换为GHz
            unit='ghz'
        )
    
    def simulate_resonator(self, resonator: ResonatorGeometry, 
                          frequency: Union[float, np.ndarray]) -> np.ndarray:
        """
        仿真单个谐振器的ABCD矩阵
        
        Args:
            resonator: 谐振器几何参数
            frequency: 频率
            
        Returns:
            ABCD矩阵数组
        """
        if np.isscalar(frequency):
            frequencies = [frequency]
        else:
            frequencies = frequency
        
        abcd_matrices = []
        
        for freq in frequencies:
            # 高阻抗段1
            line_high = MicrostripLine(resonator.high_impedance_width, self.layout.substrate)
            gamma_high = line_high.propagation_constant(freq)
            z0_high = line_high.characteristic_impedance_frequency(freq)
            
            abcd_high1 = TransmissionMatrix.transmission_line_abcd(
                gamma_high, z0_high, resonator.high_impedance_length
            )
            
            # 低阻抗段
            line_low = MicrostripLine(resonator.low_impedance_width, self.layout.substrate)
            gamma_low = line_low.propagation_constant(freq)
            z0_low = line_low.characteristic_impedance_frequency(freq)
            
            abcd_low = TransmissionMatrix.transmission_line_abcd(
                gamma_low, z0_low, resonator.low_impedance_length
            )
            
            # 高阻抗段2
            abcd_high2 = TransmissionMatrix.transmission_line_abcd(
                gamma_high, z0_high, resonator.high_impedance_length
            )
            
            # 级联
            abcd_total = TransmissionMatrix.cascade_abcd([abcd_high1, abcd_low, abcd_high2])
            abcd_matrices.append(abcd_total)
        
        if np.isscalar(frequency):
            return abcd_matrices[0]
        else:
            return np.array(abcd_matrices)
    
    def simulate_coupling(self, coupling: CouplingStructure,
                         frequency: Union[float, np.ndarray]) -> np.ndarray:
        """
        仿真耦合结构的ABCD矩阵
        
        Args:
            coupling: 耦合结构参数
            frequency: 频率
            
        Returns:
            ABCD矩阵数组
        """
        if np.isscalar(frequency):
            frequencies = [frequency]
        else:
            frequencies = frequency
        
        abcd_matrices = []
        
        for freq in frequencies:
            # 简化的耦合模型：传输线 + 集总耦合
            omega = 2 * np.pi * freq
            
            # 耦合电容 (简化模型)
            C_coupling = coupling.coupling_coefficient * 1e-12  # pF级别
            Y_coupling = 1j * omega * C_coupling
            
            # 传输线段
            line = MicrostripLine(
                width=self.layout.resonators[0].low_impedance_width,  # 使用第一个谐振器的低阻抗宽度
                substrate=self.layout.substrate
            )
            gamma = line.propagation_constant(freq)
            z0 = line.characteristic_impedance_frequency(freq)
            
            abcd_line = TransmissionMatrix.transmission_line_abcd(
                gamma, z0, coupling.gap_width
            )
            
            # 并联耦合导纳
            abcd_coupling = TransmissionMatrix.shunt_admittance_abcd(Y_coupling)
            
            # 级联
            abcd_total = TransmissionMatrix.cascade_abcd([abcd_line, abcd_coupling])
            abcd_matrices.append(abcd_total)
        
        if np.isscalar(frequency):
            return abcd_matrices[0]
        else:
            return np.array(abcd_matrices)
    
    def simulate_filter(self) -> rf.Network:
        """
        仿真完整滤波器的S参数
        
        Returns:
            scikit-rf网络对象
        """
        s_parameters = np.zeros((len(self.frequency), 2, 2), dtype=complex)
        
        for i, freq in enumerate(self.frequency):
            abcd_matrices = []
            
            # 输入线
            input_line = MicrostripLine(
                self.layout.input_line.high_impedance_width,
                self.layout.substrate
            )
            gamma_input = input_line.propagation_constant(freq)
            z0_input = input_line.characteristic_impedance_frequency(freq)
            
            abcd_input = TransmissionMatrix.transmission_line_abcd(
                gamma_input, z0_input, self.layout.input_line.total_length
            )
            abcd_matrices.append(abcd_input)
            
            # 谐振器和耦合
            for j, resonator in enumerate(self.layout.resonators):
                # 谐振器
                abcd_res = self.simulate_resonator(resonator, freq)
                abcd_matrices.append(abcd_res)
                
                # 耦合 (除了最后一个谐振器)
                if j < len(self.layout.resonators) - 1:
                    coupling = self.layout.couplings[j]
                    abcd_coup = self.simulate_coupling(coupling, freq)
                    abcd_matrices.append(abcd_coup)
            
            # 输出线
            output_line = MicrostripLine(
                self.layout.output_line.high_impedance_width,
                self.layout.substrate
            )
            gamma_output = output_line.propagation_constant(freq)
            z0_output = output_line.characteristic_impedance_frequency(freq)
            
            abcd_output = TransmissionMatrix.transmission_line_abcd(
                gamma_output, z0_output, self.layout.output_line.total_length
            )
            abcd_matrices.append(abcd_output)
            
            # 级联所有ABCD矩阵
            abcd_total = TransmissionMatrix.cascade_abcd(abcd_matrices)
            
            # 转换为S参数
            s_matrix = TransmissionMatrix.abcd_to_s_parameters(
                abcd_total, self.sim_params.port_impedance
            )
            s_parameters[i] = s_matrix
        
        # 创建scikit-rf网络对象
        network = rf.Network(
            frequency=self.freq_obj,
            s=s_parameters,
            z0=self.sim_params.port_impedance
        )
        
        return network
    
    def calculate_performance_metrics(self, network: rf.Network) -> Dict[str, np.ndarray]:
        """
        计算性能指标
        
        Args:
            network: scikit-rf网络对象
            
        Returns:
            性能指标字典
        """
        # S参数幅度 (dB)
        s11_db = 20 * np.log10(np.abs(network.s[:, 0, 0]))
        s21_db = 20 * np.log10(np.abs(network.s[:, 1, 0]))
        s12_db = 20 * np.log10(np.abs(network.s[:, 0, 1]))
        s22_db = 20 * np.log10(np.abs(network.s[:, 1, 1]))
        
        # 插入损耗和回波损耗
        insertion_loss = -s21_db
        return_loss = -s11_db
        
        # 相位
        s11_phase = np.angle(network.s[:, 0, 0], deg=True)
        s21_phase = np.angle(network.s[:, 1, 0], deg=True)
        
        # 群延迟
        group_delay = -np.gradient(s21_phase, self.frequency) / (2 * np.pi)
        
        # VSWR
        vswr = (1 + np.abs(network.s[:, 0, 0])) / (1 - np.abs(network.s[:, 0, 0]))
        
        return {
            'frequency': self.frequency,
            's11_db': s11_db,
            's21_db': s21_db,
            's12_db': s12_db,
            's22_db': s22_db,
            'insertion_loss': insertion_loss,
            'return_loss': return_loss,
            's11_phase': s11_phase,
            's21_phase': s21_phase,
            'group_delay': group_delay,
            'vswr': vswr
        }


class NetworkAnalyzer:
    """网络分析器工具类"""
    
    @staticmethod
    def find_3db_bandwidth(frequency: np.ndarray, s21_db: np.ndarray) -> Tuple[float, float, float]:
        """
        计算3dB带宽
        
        Args:
            frequency: 频率数组
            s21_db: S21幅度 (dB)
            
        Returns:
            (中心频率, 下3dB频率, 上3dB频率)
        """
        # 找到最大值
        max_idx = np.argmax(s21_db)
        max_value = s21_db[max_idx]
        center_freq = frequency[max_idx]
        
        # 3dB点
        target_value = max_value - 3.0
        
        # 找下3dB点
        lower_idx = 0
        for i in range(max_idx, 0, -1):
            if s21_db[i] <= target_value:
                lower_idx = i
                break
        
        # 找上3dB点
        upper_idx = len(frequency) - 1
        for i in range(max_idx, len(frequency)):
            if s21_db[i] <= target_value:
                upper_idx = i
                break
        
        lower_freq = frequency[lower_idx]
        upper_freq = frequency[upper_idx]
        
        return center_freq, lower_freq, upper_freq
    
    @staticmethod
    def calculate_q_factor(center_freq: float, lower_freq: float, upper_freq: float) -> float:
        """
        计算品质因子
        
        Args:
            center_freq: 中心频率
            lower_freq: 下3dB频率
            upper_freq: 上3dB频率
            
        Returns:
            品质因子
        """
        bandwidth = upper_freq - lower_freq
        if bandwidth > 0:
            return center_freq / bandwidth
        else:
            return np.inf
    
    @staticmethod
    def check_specifications(metrics: Dict[str, np.ndarray], 
                           specs: Dict[str, Dict]) -> Dict[str, bool]:
        """
        检查设计指标是否满足
        
        Args:
            metrics: 性能指标
            specs: 设计规范
            
        Returns:
            指标满足情况
        """
        results = {}
        frequency = metrics['frequency']
        
        # 通带指标检查
        if 'passband' in specs:
            passband = specs['passband']
            freq_mask = (frequency >= passband['freq_min']) & (frequency <= passband['freq_max'])
            
            # 插入损耗
            if 'max_insertion_loss' in passband:
                il_in_band = metrics['insertion_loss'][freq_mask]
                results['passband_insertion_loss'] = np.all(il_in_band <= passband['max_insertion_loss'])
            
            # 回波损耗
            if 'min_return_loss' in passband:
                rl_in_band = metrics['return_loss'][freq_mask]
                results['passband_return_loss'] = np.all(rl_in_band >= passband['min_return_loss'])
        
        # 阻带指标检查
        if 'stopband' in specs:
            stopband = specs['stopband']
            freq_mask = (frequency >= stopband['freq_min']) & (frequency <= stopband['freq_max'])
            
            # 插入损耗
            if 'min_insertion_loss' in stopband:
                il_in_stopband = metrics['insertion_loss'][freq_mask]
                results['stopband_insertion_loss'] = np.all(il_in_stopband >= stopband['min_insertion_loss'])
        
        return results
