# 1.5GHz微带线低通滤波器设计配置文件

# 滤波器设计指标
filter_specs:
  # 截止频率 (GHz)
  cutoff_frequency: 1.5
  
  # 通带要求
  passband:
    frequency_range: [0.1, 1.5]  # GHz
    max_insertion_loss: 1.0      # dB
    min_return_loss: 10.0        # dB
  
  # 阻带要求  
  stopband:
    frequency_range: [2.0, 3.0]  # GHz
    min_insertion_loss: 20.0     # dB
  
  # 滤波器类型
  filter_type: "chebyshev"
  passband_ripple: 0.5          # dB
  
  # 尺寸限制
  max_dimensions:
    length: 4.05                 # inches
    width: 1.1                   # inches

# 基板参数
substrate:
  # 材料类型
  material: "FR4"
  
  # 厚度 (inches)
  thickness: 0.062
  
  # 介电常数
  dielectric_constant: 4.6
  
  # 损耗角正切 (1GHz时)
  loss_tangent: 0.02
  loss_tangent_frequency: 1.0    # GHz
  
  # 导体厚度 (mil)
  conductor_thickness: 1.4
  
  # 导体电导率 (S/m)
  conductor_conductivity: 5.8e7  # 铜

# 仿真参数
simulation:
  # 频率范围
  frequency:
    start: 0.1                   # GHz
    stop: 5.0                    # GHz
    points: 1001
  
  # 端口阻抗
  port_impedance: 50.0           # Ohm
  
  # 收敛精度
  convergence_tolerance: 1e-6

# 优化参数
optimization:
  # 是否启用优化
  enabled: false

  # 优化算法
  algorithm: "genetic"           # genetic, particle_swarm
  
  # 遗传算法参数
  genetic:
    population_size: 50
    generations: 100
    crossover_rate: 0.8
    mutation_rate: 0.1
    elite_ratio: 0.1
  
  # 粒子群算法参数
  particle_swarm:
    swarm_size: 30
    max_iterations: 100
    inertia_weight: 0.9
    cognitive_coefficient: 2.0
    social_coefficient: 2.0
  
  # 多线程设置
  num_threads: 16
  
  # 目标函数权重
  objective_weights:
    passband_insertion_loss: 1.0
    passband_return_loss: 1.0
    stopband_insertion_loss: 2.0
    size_constraint: 0.5

# 微带线滤波器结构参数
microstrip_filter:
  # 滤波器拓扑
  topology: "stepped_impedance"  # stepped_impedance, coupled_line
  
  # 阶梯阻抗谐振器参数
  sir_params:
    # 高低阻抗比
    impedance_ratio_range: [2.0, 5.0]
    
    # 谐振器长度范围 (相对于四分之一波长)
    length_ratio_range: [0.2, 0.4]
    
    # 耦合间隙范围 (mil)
    coupling_gap_range: [5, 50]
  
  # 微带线宽度范围 (mil)
  line_width_range: [10, 200]
  
  # 微带线长度范围 (mil)  
  line_length_range: [100, 2000]

# 输出设置
output:
  # 图表设置
  plots:
    # 中文字体支持
    chinese_font: "SimHei"
    font_size: 12
    figure_size: [10, 8]
    dpi: 300
    
    # S参数图表
    s_parameters:
      frequency_unit: "GHz"
      magnitude_unit: "dB"
      phase_unit: "degree"
    
    # 布局图
    layout:
      show_dimensions: true
      show_grid: true
      grid_spacing: 0.1          # inches
  
  # 文件输出
  files:
    results_dir: "results"
    data_format: "csv"
    plot_format: "png"
    
  # 报告生成
  report:
    include_theory: true
    include_optimization_history: true
    include_sensitivity_analysis: true

# 调试设置
debug:
  verbose: true
  save_intermediate_results: true
  plot_optimization_progress: true
