#!/usr/bin/env python3
"""
1.5GHz微带线低通滤波器设计与仿真主程序

本程序实现了完整的微带线低通滤波器设计流程，包括：
1. 理论分析 - 切比雪夫滤波器设计
2. 电路设计 - 阶梯阻抗谐振器(SIR)拓扑设计
3. 仿真分析 - S参数计算和性能评估
4. 参数优化 - 多线程遗传算法/粒子群优化
5. 结果可视化 - 图表生成和数据导出

使用方法:
    python main.py [--config config_file] [--output output_dir]

作者: RF Filter Design Team
版本: 1.0.0
"""

import argparse
import sys
import time
from pathlib import Path
import traceback
import numpy as np

# 导入项目模块
from src.utils import ConfigManager, UnitConverter, logger
from src.microstrip import SubstrateProperties
from src.filter_design import ChebyshevFilter, FilterSpecifications
from src.topology import FilterTopology
from src.simulation import SParameterSimulator, SimulationParameters, NetworkAnalyzer
from src.optimization import OptimizationManager
from src.visualization import FilterVisualizer, ResultsPlotter


class FilterDesignPipeline:
    """滤波器设计流水线"""
    
    def __init__(self, config_path: str):
        """
        初始化设计流水线
        
        Args:
            config_path: 配置文件路径
        """
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        
        # 初始化组件
        self.visualizer = FilterVisualizer(self.config)
        self.results_plotter = ResultsPlotter(self.visualizer)
        
        logger.info("滤波器设计流水线初始化完成")
    
    def run_theoretical_analysis(self):
        """运行理论分析"""
        logger.info("开始理论分析...")
        
        # 创建滤波器设计指标
        specs_config = self.config['filter_specs']
        filter_specs = FilterSpecifications(
            passband_frequency=specs_config['cutoff_frequency'] * 1e9,
            stopband_frequency=specs_config['stopband']['frequency_range'][0] * 1e9,
            passband_ripple=specs_config['passband_ripple'],
            stopband_attenuation=specs_config['stopband']['min_insertion_loss'],
            source_impedance=50.0,
            load_impedance=50.0
        )
        
        # 创建切比雪夫滤波器设计
        self.chebyshev_filter = ChebyshevFilter(filter_specs)
        
        logger.info(f"滤波器阶数: {self.chebyshev_filter.order}")
        logger.info(f"归一化元件值: {self.chebyshev_filter.element_values}")
        
        # 分析理论性能
        freq_test = [0.5e9, 1.0e9, 1.5e9, 2.0e9, 2.5e9, 3.0e9]
        for f in freq_test:
            il = self.chebyshev_filter.insertion_loss(f)
            rl = self.chebyshev_filter.return_loss(f)
            logger.info(f"频率 {f/1e9:.1f} GHz: IL = {il:.2f} dB, RL = {rl:.2f} dB")
        
        return self.chebyshev_filter
    
    def run_circuit_design(self, chebyshev_filter):
        """运行电路设计"""
        logger.info("开始电路设计...")
        
        # 创建基板属性
        substrate_config = self.config['substrate']
        substrate = SubstrateProperties(
            dielectric_constant=substrate_config['dielectric_constant'],
            loss_tangent=substrate_config['loss_tangent'],
            thickness=UnitConverter.inch_to_meter(substrate_config['thickness']),
            conductor_thickness=UnitConverter.mil_to_meter(
                substrate_config.get('conductor_thickness', 1.4)
            ),
            conductor_conductivity=substrate_config.get('conductor_conductivity', 5.8e7)
        )
        
        # 创建拓扑设计器
        frequency = self.config['filter_specs']['cutoff_frequency'] * 1e9
        self.topology_designer = FilterTopology(chebyshev_filter, substrate, frequency)
        
        # 设计初始布局
        size_constraints = {
            'max_length': UnitConverter.inch_to_meter(
                self.config['filter_specs']['max_dimensions']['length']
            ),
            'max_width': UnitConverter.inch_to_meter(
                self.config['filter_specs']['max_dimensions']['width']
            )
        }
        
        self.initial_layout = self.topology_designer.design_filter_layout(size_constraints)
        
        logger.info(f"初始布局尺寸: {UnitConverter.meter_to_inch(self.initial_layout.total_length):.3f} × "
                   f"{UnitConverter.meter_to_inch(self.initial_layout.total_width):.3f} 英寸")
        
        return self.initial_layout, substrate
    
    def run_simulation(self, layout, substrate):
        """运行仿真分析"""
        logger.info("开始仿真分析...")
        
        # 创建仿真参数
        sim_config = self.config['simulation']
        sim_params = SimulationParameters(
            frequency_start=UnitConverter.ghz_to_hz(sim_config['frequency']['start']),
            frequency_stop=UnitConverter.ghz_to_hz(sim_config['frequency']['stop']),
            frequency_points=sim_config['frequency']['points'],
            port_impedance=sim_config['port_impedance']
        )
        
        # 运行S参数仿真
        simulator = SParameterSimulator(layout, sim_params)
        network = simulator.simulate_filter()
        metrics = simulator.calculate_performance_metrics(network)
        
        # 分析性能指标
        analyzer = NetworkAnalyzer()
        
        # 检查设计指标
        specs = {
            'passband': {
                'freq_min': 0.1e9,
                'freq_max': 1.5e9,
                'max_insertion_loss': 1.0,
                'min_return_loss': 10.0
            },
            'stopband': {
                'freq_min': 2.0e9,
                'freq_max': 3.0e9,
                'min_insertion_loss': 20.0
            }
        }
        
        spec_results = analyzer.check_specifications(metrics, specs)
        
        logger.info("性能指标检查结果:")
        for spec, result in spec_results.items():
            status = "通过" if result else "未通过"
            logger.info(f"  {spec}: {status}")
        
        # 计算3dB带宽
        center_freq, lower_freq, upper_freq = analyzer.find_3db_bandwidth(
            metrics['frequency'], metrics['s21_db']
        )
        bandwidth = upper_freq - lower_freq
        q_factor = analyzer.calculate_q_factor(center_freq, lower_freq, upper_freq)
        
        logger.info(f"3dB带宽: {bandwidth/1e6:.1f} MHz")
        logger.info(f"品质因子: {q_factor:.1f}")
        
        return metrics, network, spec_results
    
    def run_optimization(self):
        """运行参数优化"""
        logger.info("开始参数优化...")
        
        # 创建优化管理器
        opt_manager = OptimizationManager(self.config)
        
        # 运行优化
        optimization_result = opt_manager.run_optimization()
        
        if optimization_result.success:
            logger.info("优化成功完成!")

            # 使用优化结果重新生成布局
            weights = self.config['optimization']['objective_weights']
            from src.optimization import ObjectiveFunction
            objective_function = ObjectiveFunction(
                opt_manager.filter_specs, opt_manager.substrate, opt_manager.sim_params,
                opt_manager.design_variables, weights
            )

            optimized_layout = objective_function._generate_layout(
                objective_function._decode_individual(optimization_result.best_individual)
            )

            logger.info(f"优化后布局尺寸: "
                       f"{UnitConverter.meter_to_inch(optimized_layout.total_length):.3f} × "
                       f"{UnitConverter.meter_to_inch(optimized_layout.total_width):.3f} 英寸")

            return optimization_result, optimized_layout
        else:
            logger.warning("优化未能找到满意的解")
            return optimization_result, None
    
    def generate_results(self, metrics, layout, optimization_result=None):
        """生成结果和可视化"""
        logger.info("生成结果和可视化...")
        
        # 设计指标
        specifications = {
            'passband': {
                'frequency_range': [0.1, 1.5],
                'max_insertion_loss': 1.0,
                'min_return_loss': 10.0
            },
            'stopband': {
                'frequency_range': [2.0, 3.0],
                'min_insertion_loss': 20.0
            }
        }
        
        # 生成所有图表
        plot_paths = self.results_plotter.generate_all_plots(
            metrics, layout, optimization_result, specifications
        )
        
        # 导出数据
        csv_path = self.results_plotter.export_data(metrics, optimization_result)
        
        logger.info("结果文件生成完成:")
        logger.info(f"  数据文件: {csv_path}")
        for plot_type, path in plot_paths.items():
            logger.info(f"  {plot_type}: {path}")
        
        return plot_paths, csv_path
    
    def run_complete_design(self):
        """运行完整设计流程"""
        logger.info("="*60)
        logger.info("开始1.5GHz微带线低通滤波器设计")
        logger.info("="*60)
        
        start_time = time.time()
        
        try:
            # 1. 理论分析
            chebyshev_filter = self.run_theoretical_analysis()
            
            # 2. 电路设计
            initial_layout, substrate = self.run_circuit_design(chebyshev_filter)
            
            # 3. 初始仿真
            initial_metrics, initial_network, initial_specs = self.run_simulation(
                initial_layout, substrate
            )
            
            # 4. 参数优化
            if self.config.get('optimization', {}).get('enabled', True):
                optimization_result, optimized_layout = self.run_optimization()
                
                if optimized_layout is not None:
                    # 5. 优化后仿真
                    final_metrics, final_network, final_specs = self.run_simulation(
                        optimized_layout, substrate
                    )
                    final_layout = optimized_layout
                else:
                    final_metrics = initial_metrics
                    final_layout = initial_layout
                    optimization_result = None
            else:
                final_metrics = initial_metrics
                final_layout = initial_layout
                optimization_result = None
            
            # 6. 结果生成
            plot_paths, csv_path = self.generate_results(
                final_metrics, final_layout, optimization_result
            )
            
            # 7. 总结报告
            total_time = time.time() - start_time
            logger.info("="*60)
            logger.info("设计完成总结")
            logger.info("="*60)
            logger.info(f"总计算时间: {total_time:.2f} 秒")
            logger.info(f"滤波器阶数: {chebyshev_filter.order}")
            logger.info(f"最终布局尺寸: "
                       f"{UnitConverter.meter_to_inch(final_layout.total_length):.3f} × "
                       f"{UnitConverter.meter_to_inch(final_layout.total_width):.3f} 英寸")
            
            # 性能总结
            freq_1_5ghz_idx = np.argmin(np.abs(final_metrics['frequency'] - 1.5e9))
            freq_2_5ghz_idx = np.argmin(np.abs(final_metrics['frequency'] - 2.5e9))
            
            il_1_5ghz = final_metrics['insertion_loss'][freq_1_5ghz_idx]
            rl_1_5ghz = final_metrics['return_loss'][freq_1_5ghz_idx]
            il_2_5ghz = final_metrics['insertion_loss'][freq_2_5ghz_idx]
            
            logger.info(f"1.5GHz处性能: IL = {il_1_5ghz:.2f} dB, RL = {rl_1_5ghz:.2f} dB")
            logger.info(f"2.5GHz处插入损耗: {il_2_5ghz:.2f} dB")
            
            return True
            
        except Exception as e:
            logger.error(f"设计过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='1.5GHz微带线低通滤波器设计与仿真')
    parser.add_argument('--config', '-c', default='config/filter_config.yaml',
                       help='配置文件路径 (默认: config/filter_config.yaml)')
    parser.add_argument('--output', '-o', default='results',
                       help='输出目录 (默认: results)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出模式')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.logger.setLevel('DEBUG')
    
    # 检查配置文件
    config_path = Path(args.config)
    if not config_path.exists():
        logger.error(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)
    
    # 运行设计流程
    try:
        pipeline = FilterDesignPipeline(str(config_path))
        success = pipeline.run_complete_design()
        
        if success:
            logger.info("滤波器设计成功完成!")
            sys.exit(0)
        else:
            logger.error("滤波器设计失败!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
