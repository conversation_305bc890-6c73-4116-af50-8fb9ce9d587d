"""
微带线滤波器拓扑结构设计模块

实现阶梯阻抗谐振器(SIR)低通滤波器的物理设计，包括：
- 阶梯阻抗谐振器设计理论
- 微带线物理尺寸计算
- 滤波器布局优化
- 耦合结构设计
- 尺寸约束处理

参考文献：
1. <PERSON><PERSON><PERSON>, <PERSON>. "Microwave Resonators and Filters for Wireless Communication"
2. Hong, J. S. "Microstrip Filters for RF/Microwave Applications"
3. <PERSON><PERSON>, D. M. "Microwave Engineering"
"""

import numpy as np
from typing import List, Tuple, Dict, Optional, Union
from dataclasses import dataclass, field
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from scipy.optimize import minimize, differential_evolution

try:
    from .microstrip import MicrostripLine, MicrostripCalculator, SubstrateProperties
    from .filter_design import ChebyshevFilter, FilterSpecifications
except ImportError:
    from microstrip import MicrostripLine, MicrostripCalculator, SubstrateProperties
    from filter_design import ChebyshevFilter, FilterSpecifications


@dataclass
class ResonatorGeometry:
    """谐振器几何参数"""
    high_impedance_width: float    # 高阻抗段宽度 (m)
    low_impedance_width: float     # 低阻抗段宽度 (m)
    high_impedance_length: float   # 高阻抗段长度 (m)
    low_impedance_length: float    # 低阻抗段长度 (m)
    total_length: float            # 总长度 (m)


@dataclass
class CouplingStructure:
    """耦合结构参数"""
    gap_width: float              # 耦合间隙宽度 (m)
    coupling_length: float        # 耦合长度 (m)
    coupling_coefficient: float   # 耦合系数


@dataclass
class FilterLayout:
    """滤波器布局参数"""
    resonators: List[ResonatorGeometry]
    couplings: List[CouplingStructure]
    input_line: ResonatorGeometry
    output_line: ResonatorGeometry
    total_width: float            # 总宽度 (m)
    total_length: float           # 总长度 (m)
    substrate: SubstrateProperties


class SteppedImpedanceResonator:
    """阶梯阻抗谐振器(SIR)设计类"""
    
    def __init__(self, substrate: SubstrateProperties, frequency: float):
        """
        初始化SIR设计
        
        Args:
            substrate: 基板属性
            frequency: 工作频率 (Hz)
        """
        self.substrate = substrate
        self.frequency = frequency
        self.wavelength = self._calculate_wavelength()
    
    def _calculate_wavelength(self) -> float:
        """计算基板中的波长"""
        # 使用50欧姆微带线计算有效介电常数
        line_50ohm = MicrostripLine(
            width=MicrostripCalculator.width_from_impedance(50.0, self.substrate),
            substrate=self.substrate
        )
        er_eff = line_50ohm.effective_dielectric_constant_frequency(self.frequency)
        
        # 基板中的波长
        c = 3e8  # 光速
        wavelength = c / (self.frequency * np.sqrt(er_eff))
        return wavelength
    
    def design_sir_resonator(self, impedance_ratio: float, 
                           length_ratio: float) -> ResonatorGeometry:
        """
        设计SIR谐振器
        
        Args:
            impedance_ratio: 高低阻抗比 (Zh/Zl)
            length_ratio: 长度比 (相对于λ/4)
            
        Returns:
            谐振器几何参数
        """
        # 目标阻抗值
        Z_low = 20.0   # 低阻抗 (Ohm)
        Z_high = Z_low * impedance_ratio  # 高阻抗 (Ohm)
        
        # 计算微带线宽度
        w_low = MicrostripCalculator.width_from_impedance(Z_low, self.substrate)
        w_high = MicrostripCalculator.width_from_impedance(Z_high, self.substrate)
        
        # 计算长度
        quarter_wavelength = self.wavelength / 4
        total_length = quarter_wavelength * length_ratio
        
        # SIR长度分配 (对称结构)
        # 高阻抗段在两端，低阻抗段在中间
        l_high_total = total_length * 0.3  # 高阻抗段总长度
        l_low = total_length * 0.7         # 低阻抗段长度
        l_high = l_high_total / 2          # 单个高阻抗段长度
        
        return ResonatorGeometry(
            high_impedance_width=w_high,
            low_impedance_width=w_low,
            high_impedance_length=l_high,
            low_impedance_length=l_low,
            total_length=total_length
        )
    
    def calculate_resonant_frequency(self, geometry: ResonatorGeometry) -> float:
        """
        计算SIR谐振频率
        
        Args:
            geometry: 谐振器几何参数
            
        Returns:
            谐振频率 (Hz)
        """
        # 高低阻抗微带线
        line_high = MicrostripLine(geometry.high_impedance_width, self.substrate)
        line_low = MicrostripLine(geometry.low_impedance_width, self.substrate)
        
        # 传播常数
        gamma_high = line_high.propagation_constant(self.frequency)
        gamma_low = line_low.propagation_constant(self.frequency)
        
        beta_high = gamma_high.imag
        beta_low = gamma_low.imag
        
        # SIR谐振条件
        # tan(β_h * l_h) * tan(β_l * l_l) = (Z_h / Z_l)
        l_high = geometry.high_impedance_length
        l_low = geometry.low_impedance_length
        
        Z_high = line_high.characteristic_impedance_frequency(self.frequency)
        Z_low = line_low.characteristic_impedance_frequency(self.frequency)
        
        # 谐振条件方程
        def resonance_equation(freq):
            gamma_h = line_high.propagation_constant(freq)
            gamma_l = line_low.propagation_constant(freq)
            
            beta_h = gamma_h.imag
            beta_l = gamma_l.imag
            
            Z_h = line_high.characteristic_impedance_frequency(freq)
            Z_l = line_low.characteristic_impedance_frequency(freq)
            
            lhs = np.tan(beta_h * l_high) * np.tan(beta_l * l_low)
            rhs = Z_h / Z_l
            
            return abs(lhs - rhs)
        
        # 寻找谐振频率
        from scipy.optimize import minimize_scalar
        result = minimize_scalar(resonance_equation, 
                               bounds=(self.frequency * 0.5, self.frequency * 1.5),
                               method='bounded')
        
        return result.x
    
    def calculate_coupling_coefficient(self, gap: float, length: float,
                                     resonator1: ResonatorGeometry,
                                     resonator2: ResonatorGeometry) -> float:
        """
        计算谐振器间耦合系数
        
        Args:
            gap: 耦合间隙 (m)
            length: 耦合长度 (m)
            resonator1: 第一个谐振器
            resonator2: 第二个谐振器
            
        Returns:
            耦合系数
        """
        # 使用低阻抗段进行耦合
        w1 = resonator1.low_impedance_width
        w2 = resonator2.low_impedance_width
        w_avg = (w1 + w2) / 2
        
        # 耦合微带线参数
        coupling_params = MicrostripCalculator.coupled_microstrip_parameters(
            w_avg, gap, self.substrate
        )
        
        # 耦合系数近似计算
        k = coupling_params['coupling_coefficient']
        
        # 长度修正因子
        length_factor = np.sin(np.pi * length / self.wavelength)
        
        return k * length_factor


class FilterTopology:
    """滤波器拓扑结构设计类"""
    
    def __init__(self, filter_design: ChebyshevFilter, 
                 substrate: SubstrateProperties,
                 frequency: float):
        """
        初始化滤波器拓扑设计
        
        Args:
            filter_design: 切比雪夫滤波器设计
            substrate: 基板属性
            frequency: 工作频率 (Hz)
        """
        self.filter_design = filter_design
        self.substrate = substrate
        self.frequency = frequency
        self.sir_designer = SteppedImpedanceResonator(substrate, frequency)
        
    def design_filter_layout(self, size_constraints: Dict[str, float]) -> FilterLayout:
        """
        设计滤波器布局
        
        Args:
            size_constraints: 尺寸约束 {'max_length': m, 'max_width': m}
            
        Returns:
            滤波器布局
        """
        order = self.filter_design.order
        element_values = self.filter_design.element_values
        
        # 设计参数初始值
        impedance_ratios = np.full(order, 3.0)  # 阻抗比
        length_ratios = np.full(order, 0.25)    # 长度比
        coupling_gaps = np.full(order-1, 20e-6) # 耦合间隙 (20 mil)
        
        # 优化设计参数
        optimized_params = self._optimize_layout(
            impedance_ratios, length_ratios, coupling_gaps,
            element_values, size_constraints
        )
        
        # 生成最终布局
        layout = self._generate_layout(optimized_params, element_values)
        
        return layout

    def _optimize_layout(self, impedance_ratios: np.ndarray,
                        length_ratios: np.ndarray,
                        coupling_gaps: np.ndarray,
                        element_values: List[float],
                        size_constraints: Dict[str, float]) -> Dict[str, np.ndarray]:
        """
        优化滤波器布局参数
        """
        def objective(x):
            # 解包参数
            n_res = len(impedance_ratios)
            imp_ratios = x[:n_res]
            len_ratios = x[n_res:2*n_res]
            gaps = x[2*n_res:]

            try:
                # 计算布局
                layout = self._calculate_layout_metrics(
                    imp_ratios, len_ratios, gaps, element_values
                )

                # 目标函数：最小化尺寸 + 性能偏差
                size_penalty = 0
                if layout['total_length'] > size_constraints.get('max_length', np.inf):
                    size_penalty += 1000 * (layout['total_length'] - size_constraints['max_length'])
                if layout['total_width'] > size_constraints.get('max_width', np.inf):
                    size_penalty += 1000 * (layout['total_width'] - size_constraints['max_width'])

                performance_penalty = layout['performance_error']

                return layout['total_length'] + layout['total_width'] + size_penalty + performance_penalty

            except Exception as e:
                return 1e6

        # 参数边界
        bounds = []
        # 阻抗比边界
        for _ in range(len(impedance_ratios)):
            bounds.append((2.0, 5.0))
        # 长度比边界
        for _ in range(len(length_ratios)):
            bounds.append((0.15, 0.35))
        # 耦合间隙边界
        for _ in range(len(coupling_gaps)):
            bounds.append((5e-6, 50e-6))  # 5-50 mil

        # 初始参数
        x0 = np.concatenate([impedance_ratios, length_ratios, coupling_gaps])

        # 差分进化优化
        result = differential_evolution(objective, bounds, seed=42, maxiter=50)

        # 解包优化结果
        n_res = len(impedance_ratios)
        optimized = {
            'impedance_ratios': result.x[:n_res],
            'length_ratios': result.x[n_res:2*n_res],
            'coupling_gaps': result.x[2*n_res:]
        }

        return optimized

    def _calculate_layout_metrics(self, impedance_ratios: np.ndarray,
                                 length_ratios: np.ndarray,
                                 coupling_gaps: np.ndarray,
                                 element_values: List[float]) -> Dict[str, float]:
        """计算布局指标"""
        order = len(impedance_ratios)

        # 设计谐振器
        resonators = []
        for i in range(order):
            resonator = self.sir_designer.design_sir_resonator(
                impedance_ratios[i], length_ratios[i]
            )
            resonators.append(resonator)

        # 计算总尺寸
        total_length = sum(res.total_length for res in resonators)
        total_length += sum(coupling_gaps) * (order - 1)  # 间隙长度

        max_width = max(res.low_impedance_width for res in resonators)

        # 计算性能误差 (简化)
        performance_error = 0
        for i in range(order - 1):
            target_coupling = element_values[i+1]  # 简化的耦合目标
            actual_coupling = self.sir_designer.calculate_coupling_coefficient(
                coupling_gaps[i], resonators[i].total_length,
                resonators[i], resonators[i+1]
            )
            performance_error += abs(actual_coupling - target_coupling * 0.1)

        return {
            'total_length': total_length,
            'total_width': max_width,
            'performance_error': performance_error
        }

    def _generate_layout(self, optimized_params: Dict[str, np.ndarray],
                        element_values: List[float]) -> FilterLayout:
        """生成最终滤波器布局"""
        order = len(optimized_params['impedance_ratios'])

        # 生成谐振器
        resonators = []
        for i in range(order):
            resonator = self.sir_designer.design_sir_resonator(
                optimized_params['impedance_ratios'][i],
                optimized_params['length_ratios'][i]
            )
            resonators.append(resonator)

        # 生成耦合结构
        couplings = []
        for i in range(order - 1):
            coupling_coeff = self.sir_designer.calculate_coupling_coefficient(
                optimized_params['coupling_gaps'][i],
                resonators[i].total_length,
                resonators[i], resonators[i+1]
            )

            coupling = CouplingStructure(
                gap_width=optimized_params['coupling_gaps'][i],
                coupling_length=resonators[i].total_length,
                coupling_coefficient=coupling_coeff
            )
            couplings.append(coupling)

        # 输入输出线 (50欧姆)
        io_width = MicrostripCalculator.width_from_impedance(50.0, self.substrate)
        io_length = self.sir_designer.wavelength / 8  # λ/8长度

        input_line = ResonatorGeometry(
            high_impedance_width=io_width,
            low_impedance_width=io_width,
            high_impedance_length=io_length,
            low_impedance_length=0,
            total_length=io_length
        )

        output_line = input_line  # 对称结构

        # 计算总尺寸
        total_length = (input_line.total_length + output_line.total_length +
                       sum(res.total_length for res in resonators) +
                       sum(coup.gap_width for coup in couplings))

        total_width = max(res.low_impedance_width for res in resonators)

        return FilterLayout(
            resonators=resonators,
            couplings=couplings,
            input_line=input_line,
            output_line=output_line,
            total_width=total_width,
            total_length=total_length,
            substrate=self.substrate
        )

    def visualize_layout(self, layout: FilterLayout, save_path: Optional[str] = None):
        """
        可视化滤波器布局

        Args:
            layout: 滤波器布局
            save_path: 保存路径
        """
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))

        # 当前x位置
        x_pos = 0
        y_center = 0

        # 绘制输入线
        input_rect = patches.Rectangle(
            (x_pos, y_center - layout.input_line.high_impedance_width/2),
            layout.input_line.total_length,
            layout.input_line.high_impedance_width,
            linewidth=1, edgecolor='blue', facecolor='lightblue',
            label='Input Line'
        )
        ax.add_patch(input_rect)
        x_pos += layout.input_line.total_length

        # 绘制谐振器和耦合
        for i, (resonator, coupling) in enumerate(zip(layout.resonators, layout.couplings + [None])):
            # 高阻抗段1
            high1_rect = patches.Rectangle(
                (x_pos, y_center - resonator.high_impedance_width/2),
                resonator.high_impedance_length,
                resonator.high_impedance_width,
                linewidth=1, edgecolor='red', facecolor='lightcoral',
                label='High Z' if i == 0 else ""
            )
            ax.add_patch(high1_rect)
            x_pos += resonator.high_impedance_length

            # 低阻抗段
            low_rect = patches.Rectangle(
                (x_pos, y_center - resonator.low_impedance_width/2),
                resonator.low_impedance_length,
                resonator.low_impedance_width,
                linewidth=1, edgecolor='green', facecolor='lightgreen',
                label='Low Z' if i == 0 else ""
            )
            ax.add_patch(low_rect)
            x_pos += resonator.low_impedance_length

            # 高阻抗段2
            high2_rect = patches.Rectangle(
                (x_pos, y_center - resonator.high_impedance_width/2),
                resonator.high_impedance_length,
                resonator.high_impedance_width,
                linewidth=1, edgecolor='red', facecolor='lightcoral'
            )
            ax.add_patch(high2_rect)
            x_pos += resonator.high_impedance_length

            # 耦合间隙
            if coupling is not None:
                gap_rect = patches.Rectangle(
                    (x_pos, y_center - layout.total_width/4),
                    coupling.gap_width,
                    layout.total_width/2,
                    linewidth=1, edgecolor='gray', facecolor='white',
                    label='Coupling Gap' if i == 0 else ""
                )
                ax.add_patch(gap_rect)
                x_pos += coupling.gap_width

        # 绘制输出线
        output_rect = patches.Rectangle(
            (x_pos, y_center - layout.output_line.high_impedance_width/2),
            layout.output_line.total_length,
            layout.output_line.high_impedance_width,
            linewidth=1, edgecolor='blue', facecolor='lightblue',
            label='Output Line'
        )
        ax.add_patch(output_rect)

        # 设置图形属性
        ax.set_xlim(-layout.total_length*0.05, layout.total_length*1.05)
        ax.set_ylim(-layout.total_width*0.6, layout.total_width*0.6)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_xlabel('Length (m)')
        ax.set_ylabel('Width (m)')
        ax.set_title('Microstrip Filter Layout')

        # 添加尺寸标注
        ax.text(layout.total_length/2, layout.total_width*0.4,
                f'Total Length: {layout.total_length*1000:.1f} mm',
                ha='center', fontsize=10)
        ax.text(layout.total_length/2, layout.total_width*0.3,
                f'Total Width: {layout.total_width*1000:.1f} mm',
                ha='center', fontsize=10)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        return fig, ax
